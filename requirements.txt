# Core FastAPI dependencies
fastapi
uvicorn[standard]

# Authentication & Security
passlib[bcrypt]
python-jose[cryptography]
python-multipart

# Data validation
pydantic
pydantic_settings
pydantic[email]

# Database
motor # Async MongoDB driver
pymongo

# AI & ML
google-generativeai
langchain
langchain-ollama
langchain-community
langgraph

# Vector stores & Search
qdrant-client
requests # For Elasticsearch HTTP requests

# Data processing
pandas
dateparser

# Job scheduling
apscheduler

# Transformers (commented out for now - heavy dependencies)
# transformers>=4.40.2
# torch>=2.2.2
# accelerate>=0.29.3

# Development dependencies (uncomment for dev)
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# isort>=5.12.0
