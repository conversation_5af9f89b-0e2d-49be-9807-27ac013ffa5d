"""
Simple sync Qdrant utilities without async dependencies
"""
from qdrant_client import QdrantClient
from langchain_community.vectorstores import Qdrant as QdrantVectorStore
from langchain_ollama import OllamaEmbeddings
from app.config.index import settings
import logging
from typing import List, Any

logger = logging.getLogger(__name__)

# Global instances for reuse
_qdrant_client = None
_embedding_model = None
_vectorstore = None


def check_ollama_model_availability(model_name: str, base_url: str) -> bool:
    """Check if an Ollama model is available"""
    try:
        import requests
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get("models", [])
            available_models = [model["name"] for model in models]
            is_available = any(model_name in model for model in available_models)
            logger.info(f"Model {model_name} availability: {is_available}")
            if not is_available:
                logger.info(f"Available models: {available_models}")
            return is_available
        else:
            logger.warning(f"Could not check model availability: HTTP {response.status_code}")
            return False
    except Exception as e:
        logger.warning(f"Error checking model availability: {e}")
        return False


def get_qdrant_client_simple() -> QdrantClient:
    """Get simple Qdrant client"""
    global _qdrant_client
    if _qdrant_client is None:
        try:
            _qdrant_client = QdrantClient(
                host=settings.HOST_QDRANT, 
                port=6333,
                timeout=30
            )
            logger.info("Simple Qdrant client created successfully")
        except Exception as e:
            logger.error(f"Failed to create simple Qdrant client: {e}")
            raise
    return _qdrant_client


def get_embedding_model_simple():
    """Get simple embedding model using Ollama with fallback options"""
    global _embedding_model
    if _embedding_model is None:
        # Try different models in order of preference
        models_to_try = [
            "llama3:3b",           # User's preferred model
            "nomic-embed-text",    # Dedicated embedding model
            "llama3:8b",          # Fallback LLM
            "llama2:7b"           # Last resort
        ]

        logger.info("Checking available Ollama models...")

        for model_name in models_to_try:
            # First check if model is available
            if not check_ollama_model_availability(model_name, settings.OLLAMA_URL):
                logger.info(f"Model {model_name} not available, skipping...")
                continue

            try:
                logger.info(f"Attempting to load embedding model: {model_name}")
                _embedding_model = OllamaEmbeddings(
                    model=model_name,
                    base_url=settings.OLLAMA_URL
                )
                # Test the model with a simple query
                test_embedding = _embedding_model.embed_query("test")
                logger.info(f"✅ Ollama embedding model ({model_name}) created successfully")
                logger.info(f"Embedding dimensions: {len(test_embedding)}")
                break
            except Exception as e:
                logger.warning(f"Failed to load model {model_name}: {e}")
                _embedding_model = None
                continue

        if _embedding_model is None:
            error_msg = f"Failed to load any embedding model. Tried: {models_to_try}"
            logger.error(error_msg)
            logger.error("Please ensure Ollama is running and at least one model is available:")
            logger.error("  ollama pull llama3:3b")
            logger.error("  ollama pull nomic-embed-text")
            raise RuntimeError(error_msg)

    return _embedding_model


def get_qdrant_vectorstore_simple() -> QdrantVectorStore:
    """Get simple Qdrant vectorstore with Ollama embedding (llama3:3b)"""
    global _vectorstore
    if _vectorstore is None:
        try:
            client = get_qdrant_client_simple()
            embedding_model = get_embedding_model_simple()

            _vectorstore = QdrantVectorStore(
                client=client,
                collection_name=settings.QDRANT_COLLECTION,
                embeddings=embedding_model,
                content_payload_key="content",
                metadata_payload_key="metadata"
            )
            logger.info("Simple Qdrant vectorstore with Ollama (llama3:3b) created successfully")
        except Exception as e:
            logger.error(f"Failed to create simple Qdrant vectorstore with Ollama (llama3:3b): {e}")
            raise
    return _vectorstore


def search_qdrant_simple(question: str) -> List[Any]:
    """
    Simple sync Qdrant search using Ollama embedding (llama3:3b)
    """
    try:
        # Use direct Ollama embedding approach instead of LangChain vectorstore
        logger.info("Using Ollama embedding (llama3:3b) for Qdrant search...")
        return search_qdrant_fallback(question)

    except Exception as e:
        logger.error(f"Simple Qdrant search with Ollama (llama3:3b) failed: {e}")
        # Try with LangChain vectorstore as fallback
        try:
            logger.info("Attempting LangChain vectorstore fallback...")
            return search_qdrant_langchain_fallback(question)
        except Exception as fallback_error:
            logger.error(f"LangChain fallback also failed: {fallback_error}")
            return []


def search_qdrant_langchain_fallback(question: str) -> List[Any]:
    """
    Fallback using LangChain vectorstore with nomic-embed-text
    """
    try:
        # Get vectorstore
        vectorstore = get_qdrant_vectorstore_simple()

        # Create retriever with more conservative settings
        retriever = vectorstore.as_retriever(
            search_type="similarity",  # Use similarity instead of MMR to avoid potential issues
            search_kwargs={
                "k": 2,
                "fetch_k": 10  # Reduce fetch_k to avoid memory issues
            }
        )

        # Perform search
        logger.info(f"Performing LangChain Qdrant search for: {question[:50]}...")
        docs = retriever.invoke(question)

        logger.info(f"LangChain Qdrant search completed, found {len(docs)} documents")
        return docs

    except Exception as e:
        logger.error(f"LangChain Qdrant search failed: {e}")
        return []


def search_qdrant_fallback(question: str) -> List[Any]:
    """
    Fallback Qdrant search with minimal settings using Ollama embedding (llama3:3b)
    """
    try:
        # Get client directly
        client = get_qdrant_client_simple()

        # Get embedding model
        embedding_model = get_embedding_model_simple()

        # Get embedding for the question using Ollama (llama3:3b)
        question_vector = embedding_model.embed_query(question)

        # Direct search using client
        search_result = client.search(
            collection_name=settings.QDRANT_COLLECTION,
            query_vector=question_vector,
            limit=2,
            with_payload=True
        )

        # Convert to LangChain document format
        docs = []
        for point in search_result:
            if point.payload:
                content = point.payload.get('content', '')
                metadata = point.payload.get('metadata', {})
                # Create a simple document-like object
                doc = type('Document', (), {
                    'page_content': content,
                    'metadata': metadata
                })()
                docs.append(doc)

        logger.info(f"Fallback Qdrant search with Ollama (llama3:3b) completed, found {len(docs)} documents")
        return docs

    except Exception as e:
        logger.error(f"Fallback Qdrant search with Ollama (llama3:3b) failed: {e}")
        return []


def health_check_qdrant() -> bool:
    """
    Check Qdrant health and collection status
    """
    try:
        client = get_qdrant_client_simple()

        # Check if collection exists
        collections = client.get_collections()
        collection_names = [col.name for col in collections.collections]

        if settings.QDRANT_COLLECTION not in collection_names:
            logger.error(f"Collection '{settings.QDRANT_COLLECTION}' not found in Qdrant")
            return False

        # Get collection info
        collection_info = client.get_collection(settings.QDRANT_COLLECTION)
        logger.info(f"Qdrant collection '{settings.QDRANT_COLLECTION}' status: {collection_info.status}")
        logger.info(f"Points count: {collection_info.points_count}")
        logger.info(f"Vector size: {collection_info.config.params.vectors.size}")

        # Check vector size compatibility with llama3:3b embedding
        vector_size = collection_info.config.params.vectors.size
        if vector_size == 4096:
            logger.info("Collection appears to use 4096-dimensional embeddings (compatible with llama3:3b)")
        elif vector_size == 768:
            logger.info("Collection appears to use 768-dimensional embeddings")
        elif vector_size == 384:
            logger.info("Collection appears to use 384-dimensional embeddings")
        else:
            logger.info(f"Collection uses {vector_size}-dimensional embeddings")

        return collection_info.status == "green"

    except Exception as e:
        logger.error(f"Qdrant health check failed: {e}")
        return False


def diagnose_system() -> dict:
    """Comprehensive system diagnosis"""
    diagnosis = {
        "ollama_server": False,
        "available_models": [],
        "embedding_model": None,
        "qdrant_client": False,
        "qdrant_collection": False,
        "errors": []
    }

    try:
        # Check Ollama server
        import requests
        response = requests.get(f"{settings.OLLAMA_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            diagnosis["ollama_server"] = True
            models = response.json().get("models", [])
            diagnosis["available_models"] = [model["name"] for model in models]
        else:
            diagnosis["errors"].append(f"Ollama server returned HTTP {response.status_code}")
    except Exception as e:
        diagnosis["errors"].append(f"Cannot connect to Ollama server: {e}")

    try:
        # Check embedding model
        embedding_model = get_embedding_model_simple()
        diagnosis["embedding_model"] = embedding_model.__dict__.get("model", "unknown")
    except Exception as e:
        diagnosis["errors"].append(f"Cannot load embedding model: {e}")

    try:
        # Check Qdrant
        client = get_qdrant_client_simple()
        diagnosis["qdrant_client"] = True

        collections = client.get_collections()
        collection_names = [col.name for col in collections.collections]
        diagnosis["qdrant_collection"] = settings.QDRANT_COLLECTION in collection_names

        if not diagnosis["qdrant_collection"]:
            diagnosis["errors"].append(f"Collection '{settings.QDRANT_COLLECTION}' not found")

    except Exception as e:
        diagnosis["errors"].append(f"Qdrant error: {e}")

    return diagnosis


def close_qdrant_connections():
    """Close Qdrant connections"""
    global _qdrant_client, _embedding_model, _vectorstore

    try:
        if _qdrant_client:
            _qdrant_client.close()
            _qdrant_client = None

        _embedding_model = None
        _vectorstore = None

        logger.info("Qdrant connections closed")
    except Exception as e:
        logger.error(f"Error closing Qdrant connections: {e}")
