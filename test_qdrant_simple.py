#!/usr/bin/env python3
"""
Test script for qdrant_simple.py with llama3:3b
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.qdrant_simple import (
    health_check_qdrant,
    search_qdrant_simple,
    get_embedding_model_simple
)

def test_embedding_model():
    """Test embedding model creation"""
    print("🧪 Testing embedding model creation...")
    try:
        model = get_embedding_model_simple()
        print("✅ Embedding model created successfully")
        
        # Test embedding
        test_text = "Hello world"
        embedding = model.embed_query(test_text)
        print(f"✅ Embedding generated: {len(embedding)} dimensions")
        return True
    except Exception as e:
        print(f"❌ Embedding model test failed: {e}")
        return False

def test_health_check():
    """Test Qdrant health check"""
    print("\n🧪 Testing Qdrant health check...")
    try:
        result = health_check_qdrant()
        if result:
            print("✅ Qdrant health check passed")
        else:
            print("⚠️ Qdrant health check failed")
        return result
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_search():
    """Test Qdrant search"""
    print("\n🧪 Testing Qdrant search...")
    try:
        results = search_qdrant_simple("test query")
        print(f"✅ Search completed: {len(results)} results found")
        return True
    except Exception as e:
        print(f"❌ Search test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing qdrant_simple.py with llama3:3b")
    print("=" * 50)
    
    # Run tests
    embedding_ok = test_embedding_model()
    health_ok = test_health_check()
    search_ok = test_search()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Embedding Model: {'✅' if embedding_ok else '❌'}")
    print(f"Health Check: {'✅' if health_ok else '❌'}")
    print(f"Search Function: {'✅' if search_ok else '❌'}")
    
    if all([embedding_ok, health_ok, search_ok]):
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️ Some tests failed. Check Ollama server and llama3:3b model.")
