#!/usr/bin/env python3
"""
Debug script for qdrant_simple.py issues
"""
import sys
import os
import json
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_ollama_server():
    """Check if Ollama server is running"""
    print("🔍 Checking Ollama server...")
    try:
        import requests
        from app.config.index import settings
        
        response = requests.get(f"{settings.OLLAMA_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama server is running at {settings.OLLAMA_URL}")
            print(f"📦 Available models: {len(models)}")
            for model in models:
                print(f"   - {model['name']} (size: {model.get('size', 'unknown')})")
            return True, models
        else:
            print(f"❌ Ollama server returned HTTP {response.status_code}")
            return False, []
    except Exception as e:
        print(f"❌ Cannot connect to Ollama server: {e}")
        return False, []

def check_specific_models():
    """Check for specific models needed"""
    print("\n🔍 Checking for required models...")
    models_needed = ["llama3:3b", "nomic-embed-text", "llama3:8b"]
    
    try:
        import requests
        from app.config.index import settings
        
        response = requests.get(f"{settings.OLLAMA_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            available_models = [model["name"] for model in response.json().get("models", [])]
            
            for model in models_needed:
                found = any(model in available for available in available_models)
                status = "✅" if found else "❌"
                print(f"   {status} {model}")
                
                if not found:
                    print(f"      💡 To install: ollama pull {model}")
        else:
            print("❌ Cannot check models - Ollama server not responding")
    except Exception as e:
        print(f"❌ Error checking models: {e}")

def test_embedding_creation():
    """Test embedding model creation"""
    print("\n🔍 Testing embedding model creation...")
    try:
        from app.utils.qdrant_simple import get_embedding_model_simple
        
        model = get_embedding_model_simple()
        print("✅ Embedding model created successfully")
        
        # Test embedding
        test_text = "Hello world test"
        embedding = model.embed_query(test_text)
        print(f"✅ Test embedding successful: {len(embedding)} dimensions")
        print(f"   Sample values: {embedding[:5]}...")
        
        return True
    except Exception as e:
        print(f"❌ Embedding model creation failed: {e}")
        return False

def run_system_diagnosis():
    """Run comprehensive system diagnosis"""
    print("\n🔍 Running system diagnosis...")
    try:
        from app.utils.qdrant_simple import diagnose_system
        
        diagnosis = diagnose_system()
        print("📊 System Diagnosis Results:")
        print(f"   Ollama Server: {'✅' if diagnosis['ollama_server'] else '❌'}")
        print(f"   Available Models: {len(diagnosis['available_models'])}")
        print(f"   Embedding Model: {diagnosis['embedding_model'] or '❌'}")
        print(f"   Qdrant Client: {'✅' if diagnosis['qdrant_client'] else '❌'}")
        print(f"   Qdrant Collection: {'✅' if diagnosis['qdrant_collection'] else '❌'}")
        
        if diagnosis['errors']:
            print("\n⚠️ Errors found:")
            for error in diagnosis['errors']:
                print(f"   - {error}")
        
        return diagnosis
    except Exception as e:
        print(f"❌ System diagnosis failed: {e}")
        return None

def provide_solutions(diagnosis):
    """Provide solutions based on diagnosis"""
    print("\n💡 Recommended Solutions:")
    
    if not diagnosis['ollama_server']:
        print("1. Start Ollama server:")
        print("   - Windows: Start Ollama Desktop app")
        print("   - Linux/Mac: ollama serve")
    
    if not diagnosis['available_models']:
        print("2. Install required models:")
        print("   ollama pull llama3:3b")
        print("   ollama pull nomic-embed-text")
    
    if not diagnosis['embedding_model']:
        print("3. Check model compatibility:")
        print("   - Ensure model supports embedding")
        print("   - Try nomic-embed-text instead of llama3:3b")
    
    if not diagnosis['qdrant_client']:
        print("4. Check Qdrant server:")
        print("   - Ensure Qdrant is running")
        print("   - Check HOST_QDRANT in settings")
    
    if not diagnosis['qdrant_collection']:
        print("5. Create Qdrant collection:")
        print("   - Check QDRANT_COLLECTION name in settings")
        print("   - Ensure collection exists in Qdrant")

if __name__ == "__main__":
    print("🚀 Debugging qdrant_simple.py")
    print("=" * 50)
    
    # Run all checks
    ollama_ok, models = check_ollama_server()
    check_specific_models()
    embedding_ok = test_embedding_creation()
    diagnosis = run_system_diagnosis()
    
    print("\n" + "=" * 50)
    print("📋 Summary:")
    print(f"Ollama Server: {'✅' if ollama_ok else '❌'}")
    print(f"Embedding Model: {'✅' if embedding_ok else '❌'}")
    
    if diagnosis:
        provide_solutions(diagnosis)
    
    print("\n🔧 Quick fixes to try:")
    print("1. ollama pull llama3:3b")
    print("2. ollama pull nomic-embed-text")
    print("3. Check if Ollama server is running")
    print("4. Check if Qdrant server is running")
